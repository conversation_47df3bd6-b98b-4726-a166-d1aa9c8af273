import 'dotenv/config';

export default {
  expo: {
    name: '<PERSON><PERSON><PERSON>',
    slug: 'nasebi-app',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/icon.png',
    userInterfaceStyle: 'light',
    splash: {
      image: './assets/splash-icon.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff'
    },
    updates: {
      enabled: false
    },
    notification: {
      icon: './assets/icon.png',
      color: '#93060d',
      androidMode: 'default',
      androidCollapsedTitle: 'Nasebi',
      iosDisplayInForeground: true
    },
    assetBundlePatterns: [
      '**/*'
    ],
    ios: {
      supportsTablet: true
    },
    android: {
      package: 'com.nasebi.app',
      versionCode: 1,
      compileSdkVersion: 34,
      targetSdkVersion: 34,
      adaptiveIcon: {
        foregroundImage: './assets/adaptive-icon.png',
        backgroundColor: '#ffffff'
      },
      permissions: [
        'NOTIFICATIONS',
        'CAMERA',
        'READ_EXTERNAL_STORAGE',
        'WRITE_EXTERNAL_STORAGE'
      ]
    },
    extra: {
      stripePublishableKey: process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY ||
        'pk_test_51OpGOZFzxLuwKT1JYzL55vTzxr8iUP8FqQeN1ofUWeSZAuvsfgsLCmQDToy4wJXnEurXlR6VVRKdEYZ6cz1VZtjw00IWMZrpKb',
      // Use production backend URL
      apiUrl: process.env.EXPO_PUBLIC_API_URL || 'https://nasebi.wensh.online',
      // Project ID for push notifications
      projectId: 'nasebi-app',
      // EAS project configuration
      eas: {
        projectId: '76e11d07-59e9-4efe-83ad-73db426e21ca'
      }
    },
    plugins: [
      [
        "expo-image-picker",
        {
          "photosPermission": "The app accesses your photos to let you share them with your matches."
        }
      ],
      [
        "expo-notifications",
        {
          "icon": "./assets/icon.png",
          "color": "#93060d",
          "androidMode": "default",
          "androidCollapsedTitle": "Nasebi",
          "iosDisplayInForeground": true,
          "androidNotificationPermission": "Nasebi would like to send you notifications for new matches and messages."
        }
      ]
    ]
  }
}
