<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="Theme.ReactNative.AppCompat.Light" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColor">@android:color/black</item>
    </style>
    <style name="Theme.ReactNative.AppCompat.Light.NoActionBar.FullScreen"
           parent="@style/Theme.ReactNative.AppCompat.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="SpinnerDatePickerDialog" parent="Theme.AppCompat.Light.Dialog" tools:targetApi="lollipop">
        <item name="android:datePickerStyle">@style/SpinnerDatePickerStyle</item>
    </style>

    <style name="SpinnerDatePickerStyle" parent="android:Widget.Material.Light.DatePicker" tools:targetApi="lollipop">
        <item name="android:datePickerMode">spinner</item>
    </style>

    <style name="CalendarDatePickerDialog" parent="android:Theme.Material.Dialog.Alert" tools:targetApi="lollipop">
        <item name="android:datePickerStyle">@style/CalendarDatePickerStyle</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="CalendarDatePickerStyle" parent="android:Widget.Material.DatePicker" tools:targetApi="lollipop">
        <item name="android:datePickerMode">calendar</item>
    </style>

</resources>
