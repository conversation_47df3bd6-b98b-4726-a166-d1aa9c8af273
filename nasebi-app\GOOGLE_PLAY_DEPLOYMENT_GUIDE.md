# Google Play Bundle Deployment Guide

## ✅ Setup Complete

Your Nasebi app is now configured for Google Play deployment with:

- ✅ Dependencies installed with `--legacy-peer-deps`
- ✅ Android package name: `com.nasebi.app`
- ✅ Target SDK version: 34 (Google Play requirement)
- ✅ AAB (Android App Bundle) build configuration
- ✅ EAS Build setup for cloud builds

## 🚀 Quick Deployment Steps

### Option 1: Using Build Scripts (Recommended)

```bash
# Navigate to app directory
cd nasebi-app

# Build production AAB for Google Play
npm run build:android:production

# Or build preview APK for testing
npm run build:android:preview
```

### Option 2: Using EAS CLI Directly

```bash
# Production AAB build
eas build --platform android --profile production

# Preview APK build
eas build --platform android --profile preview
```

### Option 3: Using Batch Script (Windows)

```bash
# Run the automated build script
./build-android.bat
```

## 📋 Pre-Build Checklist

1. **Expo Account**: Make sure you're logged into EAS
   ```bash
   eas login
   ```

2. **Project Setup**: Initialize EAS project (first time only)
   ```bash
   eas build:configure
   ```

3. **Test Configuration**: Run the test script
   ```bash
   node test-build-config.js
   ```

## 🏗️ Build Process

1. **Start Build**: Run one of the build commands above
2. **Wait for Build**: EAS will build your app in the cloud (15-30 minutes)
3. **Download AAB**: Get the AAB file from EAS dashboard
4. **Upload to Google Play**: Use Google Play Console

## 📱 Google Play Console Steps

1. **Create App**: Go to Google Play Console → Create App
2. **Upload AAB**: Go to Release → Production → Create Release
3. **Upload**: Drag and drop your AAB file
4. **Fill Details**: Add app description, screenshots, etc.
5. **Submit**: Submit for review

## 🔧 Configuration Details

### App Configuration (`app.config.js`)
- App Name: Nasebi
- Package: com.nasebi.app
- Version: 1.0.0
- Target SDK: 34
- Permissions: Camera, Storage, Notifications

### Build Configuration (`eas.json`)
- Production: AAB format for Google Play
- Preview: APK format for testing
- Development: Development client

## 🛠️ Troubleshooting

### Common Issues:

1. **Build Fails**: Check EAS dashboard for detailed logs
2. **Permission Errors**: Ensure all required permissions are in app.config.js
3. **Version Conflicts**: Use `--legacy-peer-deps` flag

### Useful Commands:

```bash
# Check project health
npx expo-doctor

# View build status
eas build:list

# Cancel running build
eas build:cancel

# View build logs
eas build:view [build-id]
```

## 📊 Next Steps After Deployment

1. **Test on Device**: Install the AAB on test devices
2. **Internal Testing**: Use Google Play's internal testing
3. **Gradual Rollout**: Start with small percentage of users
4. **Monitor**: Check crash reports and user feedback

## 🔄 Future Updates

To update your app:

1. Increment version in `app.config.js`
2. Increment `versionCode` in `app.config.js`
3. Run build command again
4. Upload new AAB to Google Play Console

## 📞 Support

If you encounter issues:
- Check EAS Build documentation
- Review Google Play Console help
- Check Expo Discord community

---

**Ready to deploy!** 🎉

Run `npm run build:android:production` to start your first Google Play build.
