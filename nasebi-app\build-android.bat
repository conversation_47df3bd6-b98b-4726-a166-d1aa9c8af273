@echo off
echo ========================================
echo    Nasebi App - Google Play Builder
echo ========================================
echo.

echo Step 1: Testing configuration...
node test-build-config.js
if %errorlevel% neq 0 (
    echo Configuration test failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Installing dependencies with legacy peer deps...
npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Step 3: Checking EAS login status...
eas whoami
if %errorlevel% neq 0 (
    echo Please login to EAS first: eas login
    pause
    exit /b 1
)

echo.
echo Step 4: Building production AAB bundle...
echo This will take 15-30 minutes...
eas build --platform android --profile production
if %errorlevel% neq 0 (
    echo Failed to build AAB bundle
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully! 🎉
echo ========================================
echo.
echo Next steps:
echo 1. Download the AAB file from EAS dashboard
echo 2. Upload to Google Play Console
echo 3. Submit for review
echo.
echo You can also submit directly using: npm run submit:android
pause
