const fs = require('fs');
const path = require('path');

console.log('🔍 Testing build configuration...\n');

// Check if required files exist
const requiredFiles = [
  'app.config.js',
  'eas.json',
  'package.json'
];

console.log('📁 Checking required files:');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
  }
});

// Check app.config.js
console.log('\n📱 Checking app.config.js:');
try {
  // Read the file content and check for key configurations
  const configContent = fs.readFileSync('app.config.js', 'utf8');

  // Extract key values using regex
  const nameMatch = configContent.match(/name:\s*['"`]([^'"`]+)['"`]/);
  const packageMatch = configContent.match(/package:\s*['"`]([^'"`]+)['"`]/);
  const versionMatch = configContent.match(/version:\s*['"`]([^'"`]+)['"`]/);
  const versionCodeMatch = configContent.match(/versionCode:\s*(\d+)/);
  const targetSdkMatch = configContent.match(/targetSdkVersion:\s*(\d+)/);
  const compileSdkMatch = configContent.match(/compileSdkVersion:\s*(\d+)/);

  console.log(`✅ App name: ${nameMatch ? nameMatch[1] : 'Not found'}`);
  console.log(`✅ Package: ${packageMatch ? packageMatch[1] : 'Not found'}`);
  console.log(`✅ Version: ${versionMatch ? versionMatch[1] : 'Not found'}`);
  console.log(`✅ Version code: ${versionCodeMatch ? versionCodeMatch[1] : 'Not found'}`);
  console.log(`✅ Target SDK: ${targetSdkMatch ? targetSdkMatch[1] : 'Not found'}`);
  console.log(`✅ Compile SDK: ${compileSdkMatch ? compileSdkMatch[1] : 'Not found'}`);
} catch (error) {
  console.log(`❌ Error reading app.config.js: ${error.message}`);
}

// Check eas.json
console.log('\n🏗️ Checking eas.json:');
try {
  const easConfig = JSON.parse(fs.readFileSync('eas.json', 'utf8'));
  
  if (easConfig.build?.production?.android?.buildType) {
    console.log(`✅ Production build type: ${easConfig.build.production.android.buildType}`);
  } else {
    console.log('❌ Production build type not configured');
  }
  
  if (easConfig.build?.preview?.android?.buildType) {
    console.log(`✅ Preview build type: ${easConfig.build.preview.android.buildType}`);
  } else {
    console.log('❌ Preview build type not configured');
  }
} catch (error) {
  console.log(`❌ Error reading eas.json: ${error.message}`);
}

// Check package.json scripts
console.log('\n📦 Checking package.json scripts:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  const buildScripts = Object.keys(packageJson.scripts || {}).filter(script => 
    script.includes('build') || script.includes('submit')
  );
  
  if (buildScripts.length > 0) {
    buildScripts.forEach(script => {
      console.log(`✅ ${script}: ${packageJson.scripts[script]}`);
    });
  } else {
    console.log('❌ No build scripts found');
  }
} catch (error) {
  console.log(`❌ Error reading package.json: ${error.message}`);
}

console.log('\n🎯 Build configuration test completed!');
console.log('\n📋 Next steps:');
console.log('1. Run: eas build --platform android --profile production');
console.log('2. Or use the build script: npm run build:android:production');
console.log('3. Download the AAB file from EAS dashboard');
console.log('4. Upload to Google Play Console');
