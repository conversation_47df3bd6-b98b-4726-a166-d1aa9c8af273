# EAS Build ignore file
# Files and directories to exclude from EAS builds

# Development files
*.log
*.tmp
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Documentation
*.md
docs/
GOOGLE_PLAY_DEPLOYMENT_GUIDE.md

# Test files
test-build-config.js
build-android.bat

# Build artifacts
dist/
build/

# Environment files (keep .env for build)
.env.local
.env.development
.env.test

# Git
.git/
.gitignore

# NPM
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Expo
.expo/
.expo-shared/

# Node modules are handled by EAS
# node_modules/
